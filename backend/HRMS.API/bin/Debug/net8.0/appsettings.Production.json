{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Error"}}, "AllowedHosts": "office.plansquare.co;localhost", "Urls": "http://+:80", "ConnectionStrings": {"DefaultConnection": "Server=**************,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;"}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long-for-production", "Issuer": "HRMS.API", "Audience": "HRMS.Client", "ExpirationMinutes": 480}, "Database": {"SchemaPrefix": "org_", "SchemaSuffix": "", "AutoProvision": true, "TimeoutSeconds": 300, "ConnectionRetryCount": 5, "ConnectionRetryDelay": 10}, "AdminCredentials": {"GenerateOnStartup": false, "RequirePasswordReset": true, "SuperAdmin": {"Email": "<EMAIL>", "Name": "System Administrator"}}, "Security": {"EnableMobileBlocking": true, "MobileBlockingMessage": "This HRMS application is designed for desktop/laptop computers only. Please access from a desktop or laptop computer.", "AllowedDeviceTypes": ["Desktop", "Laptop"], "BlockedDeviceTypes": ["Mobile", "Tablet", "Smartphone"]}}