{"Version": 1, "Hash": "xXCSoJNQck3scCH6cssiwVLEVKqtw5YgRM8xUkqImXs=", "Source": "HRMS.API", "BasePath": "_content/HRMS.API", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "HRMS.API/wwwroot", "Source": "HRMS.API", "ContentRoot": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/", "BasePath": "_content/HRMS.API", "Pattern": "**"}], "Assets": [{"Identity": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/assets/index-BIyM6Mm6.js", "SourceId": "HRMS.API", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/", "BasePath": "_content/HRMS.API", "RelativePath": "assets/index-BIyM6Mm6.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/assets/index-BIyM6Mm6.js"}, {"Identity": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/assets/index-BVIVrRHu.css", "SourceId": "HRMS.API", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/", "BasePath": "_content/HRMS.API", "RelativePath": "assets/index-BVIVrRHu.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/assets/index-BVIVrRHu.css"}, {"Identity": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/favicon.ico", "SourceId": "HRMS.API", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/", "BasePath": "_content/HRMS.API", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.ico"}, {"Identity": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/index.html", "SourceId": "HRMS.API", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/", "BasePath": "_content/HRMS.API", "RelativePath": "index.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/index.html"}, {"Identity": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/placeholder.svg", "SourceId": "HRMS.API", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/", "BasePath": "_content/HRMS.API", "RelativePath": "placeholder.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/placeholder.svg"}, {"Identity": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/robots.txt", "SourceId": "HRMS.API", "SourceType": "Discovered", "ContentRoot": "/Users/<USER>/Desktop/PlanSquare/hrms-plan/backend/HRMS.API/wwwroot/", "BasePath": "_content/HRMS.API", "RelativePath": "robots.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/robots.txt"}]}