import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import ComingSoon from '@/components/ui/coming-soon';
import { 
  User, 
  MapPin, 
  Phone, 
  Mail, 
  Calendar, 
  Briefcase, 
  DollarSign, 
  GraduationCap, 
  Clock,
  Building2,
  Edit,
  Download
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const EmployeeDetailsPage = () => {
  const { user } = useAuth();

  // Get comprehensive employee data from the user context
  const employeeData = user?.employeeDetails || {
    employeeId: 'N/A',
    jobTitle: 'N/A',
    department: 'N/A',
    manager: 'N/A',
    joinDate: 'N/A',
    employmentType: 'N/A',
    employmentStatus: 'N/A',
    workLocation: 'N/A',
    phone: 'N/A',
    address: 'N/A',
    dateOfBirth: 'N/A',
    baseSalary: 'N/A',
    annualCTC: 'N/A',
    nextSalaryReview: 'N/A',
    education: [],
    skills: [],
    performanceRating: 0,
    totalExperience: 'N/A'
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Employee Details</h1>
          <p className="text-muted-foreground mt-1">Complete overview of your employment information</p>
        </div>
        <Button variant="outline" className="flex items-center space-x-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground">
          <Edit className="h-4 w-4" />
          <span>Edit Profile</span>
        </Button>
      </div>

      {/* Personal Information */}
      <Card className="border border-border bg-card">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Personal Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex flex-col items-center md:items-start">
              <Avatar className="h-32 w-32 mb-4">
                <AvatarImage src={user?.avatar} />
                <AvatarFallback className="text-2xl">
                  {user?.name?.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <Badge className="mb-2">{employeeData.jobTitle}</Badge>
              <p className="text-sm text-gray-600">{employeeData.department}</p>
            </div>
            
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Full Name</p>
                    <p className="font-medium">{user?.name}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Email</p>
                    <p className="font-medium">{user?.email}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Phone</p>
                    <p className="font-medium">{employeeData.phone}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Address</p>
                    <p className="font-medium">{employeeData.address}</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Date of Birth</p>
                    <p className="font-medium">{employeeData.dateOfBirth}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Employee ID</p>
                    <p className="font-medium">{employeeData.employeeId}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Building2 className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Organization</p>
                    <p className="font-medium">{user?.organization?.name}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Badge className="bg-green-100 text-green-800">
                    {employeeData.employmentStatus}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Employment Details */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Briefcase className="h-5 w-5" />
            <span>Employment Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Job Title</p>
              <p className="font-medium text-lg">{employeeData.jobTitle}</p>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Department</p>
              <p className="font-medium text-lg">{employeeData.department}</p>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Reporting Manager</p>
              <p className="font-medium text-lg">{employeeData.manager}</p>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Join Date</p>
              <p className="font-medium text-lg">{employeeData.joinDate}</p>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Employment Type</p>
              <p className="font-medium text-lg">{employeeData.employmentType}</p>
            </div>
            
            <div className="space-y-2">
              <p className="text-sm text-gray-600">Work Location</p>
              <p className="font-medium text-lg">{employeeData.workLocation}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compensation Overview */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5" />
            <span>Compensation Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg text-white">
              <p className="text-sm opacity-90">Base Salary</p>
              <p className="text-2xl font-bold">{employeeData.baseSalary}</p>
              <p className="text-xs opacity-80">Per Month</p>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg text-white">
              <p className="text-sm opacity-90">Annual CTC</p>
              <p className="text-2xl font-bold">{employeeData.annualCTC}</p>
              <p className="text-xs opacity-80">Per Year</p>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg text-white">
              <p className="text-sm opacity-90">Next Review</p>
              <p className="text-2xl font-bold">{employeeData.nextSalaryReview}</p>
              <p className="text-xs opacity-80">Scheduled</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Skills & Education */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <GraduationCap className="h-5 w-5" />
              <span>Education</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {employeeData.education?.map((edu: any, index: number) => (
                <div key={index} className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold">{edu.degree}</h4>
                  <p className="text-sm text-gray-600">{edu.institution}</p>
                  <p className="text-xs text-gray-500">{edu.year}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Skills</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {employeeData.skills?.map((skill: any, index: number) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{skill.name}</span>
                    <span className="text-xs text-gray-500">{skill.level}%</span>
                  </div>
                  <Progress value={skill.level} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payroll Section */}
      <Card className="border-0 shadow-lg bg-white/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>Payroll Information</span>
            </span>
            <Button size="sm" variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Payslip
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ComingSoon
            title="Payroll Details Coming Soon"
            description="Detailed payroll information including salary breakdown, tax details, and benefits will be available in this section soon."
            className="border-0 shadow-none bg-transparent"
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default EmployeeDetailsPage;