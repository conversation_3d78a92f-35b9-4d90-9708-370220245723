import { Card, CardContent } from '@/components/ui/card';
import { Clock, Wrench } from 'lucide-react';

interface ComingSoonProps {
  title?: string;
  description?: string;
  className?: string;
}

const ComingSoon = ({ 
  title = "Coming Soon", 
  description = "This feature is currently under development and will be available soon.",
  className = ""
}: ComingSoonProps) => {
  return (
    <Card className={`border-0 shadow-lg bg-white/50 backdrop-blur-sm ${className}`}>
      <CardContent className="flex flex-col items-center justify-center py-16 px-8 text-center">
        <div className="relative mb-6">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
            <Wrench className="h-10 w-10 text-blue-600" />
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
            <Clock className="h-4 w-4 text-orange-600" />
          </div>
        </div>
        
        <h3 className="text-2xl font-bold text-gray-900 mb-3">{title}</h3>
        <p className="text-gray-600 max-w-md leading-relaxed">{description}</p>
        
        <div className="mt-8 flex items-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          <span className="ml-2">Development in progress</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default ComingSoon;
